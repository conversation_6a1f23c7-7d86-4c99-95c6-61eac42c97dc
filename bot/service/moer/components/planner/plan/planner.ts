import dayjs from 'dayjs'
import logger from '../../../../../model/logger/logger'
import { TaskManager } from '../task/task_manager'
import { Job, Queue } from 'bullmq'
import { getBotId } from '../../../../../config/chat_id'
import { RedisDB } from '../../../../../model/redis/redis'
import { SchedulePlanItem, TaskScheduler } from '../task/task_scheduler'
import { listSOPByChatId } from '../../flow/schedule/task_starter'
import { SalesNodeHelper } from '../../flow/helper/salesNodeHelper'
import { ITask } from '../types'
import { RedisCacheDB } from '../../../../../model/redis/redis_cache'
import { PrismaMongoClient } from '../../../../../model/mongodb/prisma'
import { TaskWorker } from '../task/task_worker'
import { TaskStatus } from '@prisma/client'
import { ContextBuilder } from '../../agent/context'
import { UUID } from '../../../../../lib/uuid/uuid'

interface IScheduleTask {
  task_id: string
  urgency_level: 'urgent' | 'normal'
  task_type: 'daily_greeting' | 'pre_class_reminder' | 'post_class_follow_up' | 'engagement_prompt' | 'value_delivery'
  scheduled_time: string // YYYY-MM-DD HH:MM:SS 或 "now"

  description: string // 任务描述
  chat_id: string
}

export class Planner {
  /**
   * 将任务转为定时发送的 SOP
   * @param chat_id
   * @param tasks
   */
  public static async taskSchedule(chat_id: string, tasks:  ITask[]) {
    // context 拼装
    const currentTime = await ContextBuilder.getTimeInformation(chat_id)

    const tasksToSchedule = tasks
      .filter((task) => task.send_time === null) // 已经规划过的任务，不再进行二次规划
      .map((task) => {
        return {
          task_id: task.id.slice(-4),
          task_description: task.description
        }
      })

    // 获取 visualized sop 和 planner 消息队列
    const existing_schedule = await this.filterSOPByDate(chat_id, new Date(), new Date(Date.now() + 3 * 24 * 60 * 60 * 1000))

    const customerBehavior = await ContextBuilder.getCustomerPortrait(chat_id)
    const dialogHistory = await SalesNodeHelper.getChatHistory(chat_id, 3, 10)

    const scheduledTask =  await new TaskScheduler(chat_id).scheduleTask({
      user_profile: `${customerBehavior}\n\n${  dialogHistory}`,
      current_time: currentTime,
      tasks_to_schedule: tasksToSchedule,
      existing_schedule: existing_schedule
    })

    // 对 task Id 进行还原
    const idMap = tasks.reduce((map, task) => {
      map[task.id.slice(-4)] = task.id
      return map
    }, {} as Record<string, string>)


    // 假设 scheduledTask 是 [{ task_id: 'a1b2', ... }, ...]
    const scheduledTasks = scheduledTask
      .map((item) => {
        const fullTaskId = idMap[item.task_id]
        if (!fullTaskId) {
          logger.error('任务调度', `无效的任务ID: ${item.task_id}`)
          return null
        }
        return {
          ...item,
          task_id: fullTaskId // 还原完整 id
        }
      })
      .filter((task) => task !== null) as SchedulePlanItem[]

    // 更新发送时间 - 只处理有效的任务
    await Promise.allSettled(scheduledTasks.map(async (task) => {
      await TaskManager.updateTask(task.task_id, { send_time: task.scheduled_time })
    }))


    logger.log('规划任务时间', JSON.stringify(scheduledTasks, null, 4))
    return scheduledTasks
  }

  /**
   * 添加延迟任务到消息队列
   * @param chat_id
   * @param task_ids
   */
  public static async addDelayedTask(chat_id: string, task_ids: string[]) {
    const queue = new Queue<IScheduleTask>(this.getPlannerSOPQueueName(getBotId(chat_id)), {
      connection: RedisDB.getInstance()
    })

    const jobs: {name: string, data: any, opts: any}[] = []

    // 通过 task_id 查询出原始的 task
    for (const taskId of task_ids) {
      const task = await TaskManager.getTaskById(taskId)

      if (!task) continue
      if (task.status !== 'TODO') continue

      try {
        jobs.push({
          name: task.description,
          data: task,
          opts: { delay: new Date(task.send_time as string).getTime() - Date.now() }
        })
      } catch (e) {
        logger.error(e)
      }
    }

    await queue.addBulk(jobs)
  }

  public static getPlannerSOPQueueName(botId:string) {
    return `moer-planner-sop-${botId}`
  }

  private static async listSOPByChatId(chatId: string): Promise<Job[]> {
    const queue = new Queue<IScheduleTask>(this.getPlannerSOPQueueName(getBotId(chatId)), {
      connection: RedisDB.getInstance()
    })

    const allJobs = await queue.getDelayed()
    return allJobs.filter((item) => item.data.chat_id === chatId)
  }


  public static async filterSOPByDate(chatId: string, startDate: Date, endDate: Date) {
    const sops = await listSOPByChatId(chatId)
    const plannerSOPS = await Planner.listSOPByChatId(chatId)

    // 获取从开始日期到 endDate 期间的 job
    const filteredSOPs = sops.filter((item) => {
      const jobTime = new Date(item.delay + item.timestamp)
      return jobTime >= startDate && jobTime <= endDate
    })

    const filteredPlannerSOPs = plannerSOPS.filter((item) => {
      const jobTime = new Date(item.delay + item.timestamp)
      return jobTime >= startDate && jobTime <= endDate
    })

    // redis 中取回对应的 title
    const sopValue = await new RedisCacheDB(`moer:${getBotId(chatId)}:visualized_sop`).get()

    // 构建 SOP Map
    const sopMap = new Map()
    for (const sop of sopValue) {
      sopMap.set(sop.id, sop.title)
    }

    for (const sop of filteredSOPs) {
      if (!sopMap.has(sop.name)) {
        logger.error('没有这个sop', sop.name)
        continue
      }
      sop.name = sopMap.get(sop.name)
    }

    // 合并 并 以 时间排序
    const mergedSOPs = filteredSOPs.map((item) => {
      return {
        description: item.name,
        time: new Date(item.delay + item.timestamp)
      }
    }).concat(filteredPlannerSOPs.map((item) => {
      return {
        description: item.name,
        time: new Date(item.delay + item.timestamp)
      }
    })).sort((a, b) => a.time.getTime() - b.time.getTime())

    // 描述 + 时间
    return mergedSOPs.map((item) => {
      return {
        description: item.description,
        time: dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')
      }
    })
  }


  static async executeImmediateTask(chat_id: string, scheduledTasks: SchedulePlanItem[]) {
    // 获取任务描述进行合并
    // 执行任务
    // 更新任务状态
    const immediateTasks = scheduledTasks.filter((item) => item.scheduled_time === 'now')

    const tasks = await PrismaMongoClient.getInstance().task.findMany({
      where: {
        chat_id,
        id: {
          in: immediateTasks.map((item) => item.task_id)
        }
      }
    })

    // 合并 Task 描述
    const taskDescription = tasks.filter((item) => item.status === 'TODO').map((item, index) => `${index + 1}. ${item.description}`).join('\n')

    // 更新任务状态
    await Promise.all(tasks.map((task) => TaskManager.updateStatus(task.id, TaskStatus.DONE)))


    if (taskDescription.trim() === '') {
      return
    }

    await TaskWorker.processTask(chat_id, taskDescription)
  }

  static async addPassiveTasks(chat_id: string, task: string[], round_id: string) {
    const toAddTasks = task.map((item) => ({ id: UUID.short().slice(0, 4), description: item }))
    const tasks =  await TaskManager.getFlexibleActiveTasks(chat_id)

    if (toAddTasks.length + tasks.length > 5) {
      const filteredTasks = await TaskManager.mergeTasks(chat_id, toAddTasks, tasks, round_id)
      await TaskManager.createTasks(chat_id, filteredTasks.map((item) => ({ description: item.description })), '', round_id)
    } else {
      await TaskManager.createTasks(chat_id, task.map((item) => ({ description: item })), '', round_id)
    }
  }
}